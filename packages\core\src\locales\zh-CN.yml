yesimbot:
    # 核心记忆管理器 (Memory class) 相关的国际化字符串
    memory:
        # 错误信息
        error:
            memoryBlockNotFound: "未找到指定的记忆块"
            appendCoreMemoryFailed: "追加核心记忆失败: {message}"
            replaceCoreMemoryFailed: "替换核心记忆失败: {message}"
            insertArchivalMemoryFailed: "插入档案记忆失败: {message}"
            searchArchivalMemoryFailed: "搜索档案记忆失败: {message}"
            renderMemoryFailed: "渲染记忆失败: {message}"
            disposeFailed: "清理记忆管理器资源失败: {message}"

        # 信息提示（通常用于日志或用户反馈）
        info:
            initialized: "记忆管理器已初始化"
            coreMemoryAppended: "成功向核心记忆追加内容: {label}"
            coreMemoryReplaced: "成功替换核心记忆: {label}"
            archivalMemoryInserted: "成功插入档案记忆，内容长度: {contentLength}"
            archivalMemorySearchCompleted: "档案记忆搜索完成: 查询=\"{query}\", 找到{count}个结果"

        # 调试信息（通常仅用于开发日志）
        debug:
            memoryBlockAdded: "已添加记忆块: {label}"

        # 成功操作的返回消息
        success:
            appendCoreMemory: "记忆追加成功。新内容: {content}"
            replaceCoreMemory: "记忆替换成功。新内容: {newContent}"
            insertArchivalMemory: "档案记忆插入成功。新内容: {content}"

        # 档案记忆搜索结果相关字符串
        search:
            noResults: "未找到包含 \"{query}\" 的档案记忆。"
            resultItem: "结果 {index}:\n{content}"
            header: "档案记忆搜索结果 (查询: \"{query}\")"
            pageInfo: "第 {page} 页，共找到 {totalCount} 个结果:"

        # 记忆状态渲染（render方法）相关字符串
        render:
            header: "### Memory [last modified: {lastModified}]"
            archivalSummary: "{count} total memories you created are stored in archival memory (use functions to access them)"
            coreMemoryIntro: "Core memory shown below (limited in size, additional information stored in archival / recall memory):"

    # 记忆块 (MemoryBlock class) 相关的国际化字符串
    memoryBlock:
        # 错误信息
        error:
            createWithoutLabel: "未找到记忆且无法在没有标签的情况下创建"
            getOrCreateFailed: "获取或创建记忆失败: {message}"
            syncFromFileFailed: "从文件同步失败: {message}"
            bindFileFailed: "绑定文件失败: {message}"
            saveToFileFailed: "保存到文件失败: {message}"
            getDataFailed: "获取记忆块数据失败: {message}"
            memoryLimitExceeded: "内存限制已超出"
            contentNotFoundToReplace: "未找到要替换的内存内容"

        # 信息提示
        info:
            newCreated: "创建新记忆: {label} ({id})"
            newFileCreated: "创建新文件: {filePath}"
            fileBound: "成功绑定文件到记忆: {label} -> {filePath}"
            cleared: "已清空记忆块: {label}"

        # 调试信息
        debug:
            created: "创建记忆: {label} ({id})"
            foundExisting: "找到现有记忆: {label}"
            syncedFromFile: "已从文件同步数据到记忆: {label}"
            savedToFile: "已保存内容到文件: {filePath}"
            contentAppended: "已追加内容到记忆块 {label}, 新大小: {newSize}"
            contentDeleted: "已从记忆块 {label} 删除内容"
            contentReplaced: "已替换记忆块 {label} 中的内容"

        # 警告信息
        warn:
            loadFromFileFailed: "从文件加载失败 {filePath}: {message}"
            memoryLimitExceededDetails: "内存限制已超出，当前大小: {currentSize}, 尝试添加: {contentLength}, 限制: {limit}"

        # 文件监视器相关字符串
        fileWatcher:
            info:
            startWatching: "[文件监视器] 开始监听文件变动 {filePath}"
            fileModified: "[文件监视器] 文件 {filePath} 已被修改，正在同步到记忆"
            disposing: "[文件监视器] 正在清理记忆块资源: {label}"
            debug:
            stopped: "[文件监视器] 已停止文件监视"
            error:
            changeHandlerFailed: "[文件监视器] 处理文件变化时出错: {message}"

        # 记忆块内容渲染相关字符串
        render:
            header: "<{label} characters=\"{currentSize}/{limit}\">"
            footer: "</{label}>"
