yesimbot:
    memory:
        error:
            memoryBlockNotFound: "Memory block not found"
            appendCoreMemoryFailed: "Failed to append core memory: {message}"
            replaceCoreMemoryFailed: "Failed to replace core memory: {message}"
            insertArchivalMemoryFailed: "Failed to insert archival memory: {message}"
            searchArchivalMemoryFailed: "Failed to search archival memory: {message}"
            renderMemoryFailed: "Failed to render memory: {message}"
            disposeFailed: "Failed to dispose memory manager resources: {message}"

        info:
            initialized: "Memory manager initialized"
            coreMemoryAppended: "Successfully appended content to core memory: {label}"
            coreMemoryReplaced: "Successfully replaced core memory: {label}"
            archivalMemoryInserted: "Successfully inserted archival memory, content length: {contentLength}"
            archivalMemorySearchCompleted: "Archival memory search completed: query=\"{query}\", found {count} results"

        debug:
            memoryBlockAdded: "Memory block added: {label}"

        success:
            appendCoreMemory: "Memory append successful. New content: {content}"
            replaceCoreMemory: "Memory replacement successful. New content: {newContent}"
            insertArchivalMemory: "Archival memory insertion successful. New content: {content}"

        search:
            noResults: "No archival memories found containing \"{query}\"."
            resultItem: "Result {index}:\n{content}"
            header: "Archival Memory Search Results (Query: \"{query}\")"
            pageInfo: "Page {page} of {totalCount} results found:"

        render:
            header: "### Memory [last modified: {lastModified}]"
            archivalSummary: "{count} total memories you created are stored in archival memory (use functions to access them)"
            coreMemoryIntro: "Core memory shown below (limited in size, additional information stored in archival / recall memory):"

    memoryBlock:
        error:
            createWithoutLabel: "Memory not found and cannot be created without a label"
            getOrCreateFailed: "Failed to get or create memory: {message}"
            syncFromFileFailed: "Failed to sync from file: {message}"
            bindFileFailed: "Failed to bind file: {message}"
            saveToFileFailed: "Failed to save to file: {message}"
            getDataFailed: "Failed to get memory block data: {message}"
            memoryLimitExceeded: "Memory limit exceeded"
            contentNotFoundToReplace: "No memory content found to replace"

        info:
            newCreated: "New memory created: {label} ({id})"
            newFileCreated: "New file created: {filePath}"
            fileBound: "Successfully bound file to memory: {label} -> {filePath}"
            cleared: "Memory block cleared: {label}"

        debug:
            created: "Memory created: {label} ({id})"
            foundExisting: "Found existing memory: {label}"
            syncedFromFile: "Data synced from file to memory: {label}"
            savedToFile: "Content saved to file: {filePath}"
            contentAppended: "Content appended to memory block {label}, new size: {newSize}"
            contentDeleted: "Content deleted from memory block {label}"
            contentReplaced: "Content replaced in memory block {label}"

        warn:
            loadFromFileFailed: "Failed to load from file {filePath}: {message}"
            memoryLimitExceededDetails: "Memory limit exceeded, current size: {currentSize}, attempt to add: {contentLength}, limit: {limit}"

        fileWatcher:
            info:
                startWatching: "[File Watcher] Starting to monitor file changes for {filePath}"
                fileModified: "[File Watcher] File {filePath} modified, syncing to memory"
                disposing: "[File Watcher] Disposing memory block resources: {label}"
            debug:
                stopped: "[File Watcher] File monitoring stopped"
            error:
                changeHandlerFailed: "[File Watcher] Error handling file change: {message}"

        render:
            header: "<{label} characters=\"{currentSize}/{limit}\">"
            footer: "</{label}>"
