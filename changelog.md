### 3.0.0-beta.1
feat(extension): 获取转发聊天记录
feat(extension): 支持查看转发记录里的图片
refactor(scenario): 优化交互处理逻辑，修复清空对话后上下文无法同步的问题
- 将 ScenarioManager 注册为服务
- 优化 Interaction 数据结构，明确区分工具调用和结果
- 改进内存中交互生命周期的同步与清理机制
- 增强错误处理和日志记录
- 优化上下文渲染格式，支持更多消息类型
- 修复消息加载和分页逻辑
feat(commands): 添加配置管理命令模块
新增 config.ts 文件实现配置的获取和设置功能，支持嵌套对象和数组索引的路径解析。
- conf.get 命令：通过键路径获取配置值
- conf.set 命令：通过键路径设置配置值
feat: 增加提示词构造器、优化提示词构造流程、添加并更新部分提示词
- 提示词支持使用{{}}占位符
- 增强防越狱机制
- 允许自定义提示词
refactor(core): 统一服务命名并重构中间件架构
重构核心模块的服务命名规范，统一使用"yesimbot"前缀。将中间件改为抽象类实现，优化依赖注入方式。删除冗余的ServiceContainer，简化服务管理。
- 将ModelService、MemoryService等服务重命名为统一命名空间
- 重构中间件为抽象类，提供更好的扩展性
- 优化服务依赖注入方式，直接通过ctx访问
- 删除不再需要的ServiceContainer及相关代码
- 调整部分命令和工具的导出方式
feat: 实现多模态支持
添加多模态配置支持，重构 Scenario 和 PromptBuilder 以处理图片和文本混合内容。
1. 引入 MultimodalConfig 配置接口，支持图片详细度和最大数量限制
2. 重构 Scenario.renderForPrompt() 返回 Part 数组而非纯文本
3. 修改 PromptBuilder 处理多模态内容，支持图片和文本混合提示词
4. 优化图片处理逻辑，添加图片计数和限制检查
5. 改进消息格式化，支持图片嵌入和文本合并
refactor(extensions): 重构工具扩展系统，引入新架构和装饰器支持
1. 移除旧的base.ts实现，引入新的类型定义和辅助函数
2. 添加装饰器支持，简化工具和扩展的定义
3. 实现工具注册表和日志系统
4. 更新所有内置扩展使用新API
5. 添加TS配置支持装饰器元数据
6. 提供多种工具定义方式示例
7. 改进MCP管理器与工具系统的集成
refactor(scenario): 重构场景模块，优化代码结构和功能实现
- 将 Scenario 相关代码迁移到 services/scenario 目录下
- 新增 ContextProcessor 用于场景消息分析
- 重命名 clearNewMessages 为 clearPendingMessages 以更准确表达功能
- 改进 Scenario 类的渲染方法，支持上下文摘要
- 优化图片处理和消息格式化逻辑
- 添加类型定义文件 types.ts
- 更新相关模块的引用路径
feat(worldstate): 实现世界状态管理服务及数据模型
新增世界状态管理服务，包含以下核心功能：
- 定义世界状态、频道、成员等核心数据接口
- 实现成员和对话回合的仓库模式数据访问层
- 提供数据管理服务用于获取频道信息和世界状态
- 扩展Koishi数据库模型以支持新功能
- 添加可选参数检查防止空指针异常
refactor(core): 重构场景管理为基于回合的世界状态系统
- 移除 ScenarioManager 及相关场景管理代码，引入 DataManager 和回合系统
- 新增 world_state.mustache 模板用于世界状态渲染
- 修改中间件以支持回合制交互，包括 DatabaseStorageMiddleware 和 ResponseHandlingMiddleware
- 更新提示模板，整合回合制世界观和工具调用框架
- 调整服务容器注册，用 DataManager 替换 ScenarioManager
- 修改核心内存服务接口，支持新的模板渲染需求

### 3.0.0-beta.0
feat(error-handling): 调整错误上报格式
refactor(memory): 重构记忆模块实现
- 将原有Memory类拆分为MemoryService、MemoryBlock等模块化组件
- 实现数据库存储与文件同步的记忆块管理
- 新增归档记忆存储功能
- 重构记忆相关工具函数，支持更细粒度的记忆操作
- 更新配置结构，移除旧的内存文件存储配置
- 优化LLM中间件中的记忆提示生成逻辑
feat(memory): 实现记忆压缩功能
- 添加记忆块的自动压缩功能，支持按行数、字符数、消息间隔和时间间隔触发压缩
- 新增压缩配置选项，包括触发条件、自定义提示词和可压缩的记忆块类型
- 添加记忆备份功能，在压缩前自动备份原始内容
- 扩展记忆服务配置，支持指定压缩使用的模型和备份路径
feat: 注册模型服务并添加识图功能
重构模型服务架构，将ChatModelSwitcher与ModelService分离，提供更灵活的模型管理。新增图片查看工具，支持通过模型分析图片内容。同时优化了交互数据结构、场景渲染格式和内存服务。
- 将ChatModelSwitcher重构为独立服务
- 添加ImageViewer配置和图片处理工具
- 优化Interaction接口数据结构
- 改进场景渲染的缩进和格式
- 增强内存服务的初始化和错误处理

### 3.0.0-alpha.16
fix: 修复语言文件导入问题
refactor(core): 重构工具管理器为插件服务并优化代码结构
- 将 ToolManager 重构为 Koishi 插件服务，移除单例模式，通过 Context 注入使用
- 优化工具定义和执行逻辑，增强类型安全性和可维护性
- 统一工具参数命名规范，修复内置工具的参数校验问题
- 添加多语言支持文件并整理代码格式

### 3.0.0-alpha.15
refactor(embeddings): 重构嵌入功能到适配器模块
fix(command): 添加参数验证
fix(extension): 修复扩展导入路径
fix(tool): 修复内置工具
feat(config): 增加最大心跳次数
feat: 优化场景管理和响应处理逻辑
- 在ResponseHandling中跳过send_message函数的交互记录
- 修改CheckReplyCondition以支持频道分组和动态回复概率
- 重构Scenario类，改进消息存储和渲染逻辑
- 在LLMProcessing中添加多频道场景渲染支持
- 增强ScenarioManager的多场景协同处理能力

### 3.0.0-alpha.14
feat: 重构场景管理和平台适配逻辑

主要变更：
1. 新增 ScenarioManager 服务集中管理场景生命周期和缓存
2. 添加 PlatformAdapter 接口实现多平台信息适配
3. 优化交互记录处理，新增 emitter_channel_id 字段
4. 重构错误处理中间件的日志输出格式
5. 移除冗余代码，改进 LLM 处理中间件的适配器切换逻辑

重构动机：
- 将场景管理逻辑从 MessageContext 解耦，提升可维护性
- 支持更灵活的多平台信息获取
- 优化交互记录的存储和查询效率
- 提供更清晰的错误日志和重试机制

### 3.0.0-alpha.13

- feat(adapter): 添加更多适配器支持
- feat(prompt): 优化 Prompt 试图解决刷屏与复读问题
- feat(processor): 使用 MD5 作为图片缓存键
- 将消息 ID 添加到上下文中
- feat(adapter): 支持设置代理服务器
- feat(adapter): 请求失败切换下一个 API

### 3.0.0-alpha.12

- fix: 修复响应失败后没有重置状态

### 3.0.0-alpha.11

- 为API和工具添加超时和重试机制
- 允许配置重试次数
- 优化初始化流程
- 增加对图片消息的处理

### 3.0.0-alpha.10

- refactor(middleware): 优化CheckReplyConditionMiddleware的回复逻辑
  - 不同用户有不同的回复阈值，优先级高的用户更容易获得回复
  - 根据历史互动质量自动调整用户优先级
  - 用户不仅影响个人意愿值，也对频道整体意愿有贡献
- feat(command): 添加清除上下文，图片缓存，扩展管理指令
- feat: 将YesImBot注册为服务

### 3.0.0-alpha.9

- fix(core): 修复响应失败后不会再次触发回复的问题
- feat(tools): 将扩展移动到builtin
- fix: 更改错误上报地址

### 3.0.0-alpha.8

- feat(tools): 修正工具参数
